import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_theme.dart';
import '../providers/ai_exercise_provider.dart';

class AiExerciseView extends StatefulWidget {
  const AiExerciseView({super.key});

  @override
  State<AiExerciseView> createState() => _AiExerciseViewState();
}

class _AiExerciseViewState extends State<AiExerciseView> {
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    // Rebuild when the text changes so the submit button state updates
    _textController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    // Remove listener before disposing
    _textController.removeListener(_onTextChanged);
    _textController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    // Call setState to update button enabled state when text changes.
    // Keep lightweight to avoid unnecessary work.
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AiExerciseProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Title
              Text(
                'تمرین نوشتاری با هوش مصنوعی',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.tealAccent[400],
                    ),
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // Subtitle
              Text(
                'متن خود را بنویسید و بازخورد هوش مصنوعی دریافت کنید',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Text input field
              _buildTextInputField(),
              const SizedBox(height: 24),

              // Submit button
              _buildSubmitButton(provider),
              const SizedBox(height: 32),

              // Content area based on state
              _buildContentArea(provider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTextInputField() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.kCardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.tealAccent[400]!.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: TextField(
        controller: _textController,
        maxLines: 8,
        minLines: 5,
        textDirection: TextDirection.rtl,
        textAlign: TextAlign.right,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              height: 1.6,
            ),
        decoration: InputDecoration(
          hintText: 'تمرین نوشتاری خود را اینجا بنویسید...',
          hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withValues(alpha: 0.5),
              ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
          hintTextDirection: TextDirection.rtl,
        ),
      ),
    );
  }

  Widget _buildSubmitButton(AiExerciseProvider provider) {
    final isButtonEnabled =
        !provider.isLoading && _textController.text.trim().isNotEmpty;

    return ElevatedButton(
      onPressed: isButtonEnabled ? () => _submitText(provider) : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.tealAccent[400],
        foregroundColor: Colors.black,
        disabledBackgroundColor: Colors.grey[600],
        disabledForegroundColor: Colors.grey[400],
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: isButtonEnabled ? 4 : 0,
      ),
      child: provider.isLoading
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'در حال دریافت بازخورد...',
                  style: const TextStyle(
                    fontFamily: 'Samim',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            )
          : Text(
              'دریافت بازخورد هوش مصنوعی',
              style: const TextStyle(
                fontFamily: 'Samim',
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
              textDirection: TextDirection.rtl,
            ),
    );
  }

  Widget _buildContentArea(AiExerciseProvider provider) {
    if (provider.isLoading) {
      return _buildLoadingState();
    }

    if (provider.error != null) {
      return _buildErrorState(provider);
    }

    if (provider.feedback != null) {
      return _buildFeedbackState(provider);
    }

    return _buildInitialState();
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.kCardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.tealAccent[400]!.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          CircularProgressIndicator(
            color: Colors.tealAccent[400],
          ),
          const SizedBox(height: 16),
          Text(
            'هوش مصنوعی در حال بررسی متن شما...',
            style: Theme.of(context).textTheme.bodyMedium,
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(AiExerciseProvider provider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.red[900]!.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red[400]!.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[400],
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            provider.error!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.red[300],
                ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () => _tryAgain(provider),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red[400],
            ),
            child: Text(
              'تلاش مجدد',
              style: const TextStyle(
                fontFamily: 'Samim',
                fontWeight: FontWeight.w600,
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackState(AiExerciseProvider provider) {
    return Card(
      color: AppTheme.kCardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.tealAccent[400]!.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.psychology,
                  color: Colors.tealAccent[400],
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'بازخورد هوش مصنوعی',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.tealAccent[400],
                        fontWeight: FontWeight.bold,
                      ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SelectableText(
              provider.feedback!,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    height: 1.8,
                  ),
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.justify,
            ),
            const SizedBox(height: 24),
            Center(
              child: TextButton(
                onPressed: () => _tryAgain(provider),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.tealAccent[400],
                ),
                child: Text(
                  'تمرین جدید',
                  style: const TextStyle(
                    fontFamily: 'Samim',
                    fontWeight: FontWeight.w600,
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.kCardColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.tealAccent[400]!.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.edit_note,
            color: Colors.tealAccent[400],
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'متن خود را در بالا بنویسید و دکمه "دریافت بازخورد" را فشار دهید',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withValues(alpha: 0.7),
                ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _submitText(AiExerciseProvider provider) {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      provider.fetchAiFeedback(userText: text);
    }
  }

  void _tryAgain(AiExerciseProvider provider) {
    provider.clear();
    _textController.clear();
  }
}
