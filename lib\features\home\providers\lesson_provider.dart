import 'package:flutter/foundation.dart';
import '../../../core/services/api_service.dart';
import '../models/lesson_model.dart';

class LessonProvider extends ChangeNotifier {
  final ApiService _apiService;

  LessonProvider(this._apiService);

  // Private fields
  List<Lesson> _lessons = [];
  bool _isLoading = false;
  String? _error;

  // Public getters
  List<Lesson> get lessons => _lessons;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Computed getters for convenience
  List<Lesson> get freeLessons => _lessons.where((lesson) => lesson.isFree).toList();
  List<Lesson> get lockedLessons => _lessons.where((lesson) => !lesson.isFree).toList();
  bool get hasLessons => _lessons.isNotEmpty;
  bool get hasError => _error != null;

  /// Load lessons from the API
  Future<void> loadLessons() async {
    _setLoading(true);
    _clearError();

    try {
      final fetchedLessons = await _apiService.fetchLessons();
      _lessons = fetchedLessons;
      _clearError();
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _lessons = []; // Clear lessons on error
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh lessons (same as loadLessons but can be used for pull-to-refresh)
  Future<void> refreshLessons() async {
    await loadLessons();
  }

  /// Retry loading lessons after an error
  Future<void> retryLoadLessons() async {
    await loadLessons();
  }

  /// Clear all data and reset to initial state
  void clearData() {
    _lessons = [];
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Clean up any resources if needed
    super.dispose();
  }
}
