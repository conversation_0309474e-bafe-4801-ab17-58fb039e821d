import 'package:flutter/foundation.dart';
import '../../../core/services/api_service.dart';
import '../../home/<USER>/lesson_model.dart';

class LessonDetailProvider extends ChangeNotifier {
  final ApiService _apiService;

  LessonDetailProvider(this._apiService);

  // Private fields
  Lesson? _lesson;
  bool _isLoading = false;
  String? _error;

  // Public getters
  Lesson? get lesson => _lesson;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasLesson => _lesson != null;
  bool get hasError => _error != null;

  /// Load lesson detail by ID from the API
  Future<void> loadLessonDetail(int id) async {
    _setLoading(true);
    _clearError();

    try {
      final fetchedLesson = await _apiService.fetchLessonById(id);
      _lesson = fetchedLesson;
      _clearError();
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _lesson = null; // Clear lesson on error
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh lesson detail (same as loadLessonDetail but can be used for pull-to-refresh)
  Future<void> refreshLessonDetail(int id) async {
    await loadLessonDetail(id);
  }

  /// Retry loading lesson detail after an error
  Future<void> retryLoadLessonDetail(int id) async {
    await loadLessonDetail(id);
  }

  /// Clear all data and reset to initial state
  void clearData() {
    _lesson = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Clean up any resources if needed
    super.dispose();
  }
}
