import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../../features/home/<USER>/lesson_model.dart';
import '../../features/lesson_detail/models/quiz_models.dart';

class ApiService {
  // Use Android emulator localhost for development
  // For iOS simulator, use http://127.0.0.1:3000/api
  final String _baseUrl = 'http://*************:3000/api';

  // Alternative URL for iOS simulator
  // static const String _baseUrl = 'http://127.0.0.1:3000/api';

  static const Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  Future<List<Lesson>> fetchLessons() async {
    try {
      final uri = Uri.parse('$_baseUrl/lessons');

      final response = await http
          .get(
        uri,
        headers: _headers,
      )
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('درخواست منقضی شد. لطفاً دوباره تلاش کنید.');
        },
      );

      if (response.statusCode == 200) {
        // پاسخ سرور یک لیست است، نه یک آبجکت
        final List<dynamic> lessonsJson = json.decode(response.body);

        return lessonsJson
            .map((lessonJson) =>
                Lesson.fromJson(lessonJson as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 404) {
        throw Exception('سرویس درس‌ها یافت نشد.');
      } else if (response.statusCode == 500) {
        throw Exception('خطای داخلی سرور. لطفاً بعداً تلاش کنید.');
      } else {
        throw Exception('خطا در دریافت درس‌ها: ${response.statusCode}');
      }
    } on SocketException {
      throw Exception(
          'خطا در اتصال به اینترنت. لطفاً اتصال خود را بررسی کنید.');
    } on FormatException {
      throw Exception('خطا در تجزیه داده‌های دریافتی از سرور.');
    } on Exception catch (e) {
      // Re-throw custom exceptions
      if (e.toString().contains('درخواست منقضی شد') ||
          e.toString().contains('فرمت پاسخ سرور') ||
          e.toString().contains('سرویس درس‌ها') ||
          e.toString().contains('خطای داخلی سرور') ||
          e.toString().contains('خطا در اتصال به اینترنت') ||
          e.toString().contains('خطا در تجزیه داده‌های')) {
        rethrow;
      }
      throw Exception('خطای غیرمنتظره: ${e.toString()}');
    } catch (e) {
      throw Exception('خطای غیرمنتظره در دریافت درس‌ها.');
    }
  }

  Future<Lesson> fetchLessonById(int id) async {
    try {
      final uri = Uri.parse('$_baseUrl/lessons/$id');

      final response = await http
          .get(
        uri,
        headers: _headers,
      )
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('درخواست منقضی شد. لطفاً دوباره تلاش کنید.');
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> lessonJson = json.decode(response.body);
        return Lesson.fromJson(lessonJson);
      } else if (response.statusCode == 404) {
        throw Exception('درس مورد نظر یافت نشد.');
      } else if (response.statusCode == 500) {
        throw Exception('خطای داخلی سرور. لطفاً بعداً تلاش کنید.');
      } else {
        throw Exception('خطا در دریافت جزئیات درس: ${response.statusCode}');
      }
    } on SocketException {
      throw Exception(
          'خطا در اتصال به اینترنت. لطفاً اتصال خود را بررسی کنید.');
    } on FormatException {
      throw Exception('خطا در تجزیه داده‌های دریافتی از سرور.');
    } on Exception catch (e) {
      // Re-throw custom exceptions
      if (e.toString().contains('درخواست منقضی شد') ||
          e.toString().contains('درس مورد نظر یافت نشد') ||
          e.toString().contains('خطای داخلی سرور') ||
          e.toString().contains('خطا در اتصال به اینترنت') ||
          e.toString().contains('خطا در تجزیه داده‌های') ||
          e.toString().contains('خطا در دریافت جزئیات درس')) {
        rethrow;
      }
      throw Exception('خطای غیرمنتظره: ${e.toString()}');
    } catch (e) {
      throw Exception('خطای غیرمنتظره در دریافت جزئیات درس.');
    }
  }

  Future<String> getAiFeedback({required String userText}) async {
    try {
      final uri = Uri.parse('$_baseUrl/ai/feedback');
      final requestBody = json.encode({'user_text': userText});

      final response = await http
          .post(
        uri,
        headers: _headers,
        body: requestBody,
      )
          .timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('درخواست منقضی شد. لطفاً دوباره تلاش کنید.');
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseJson = json.decode(response.body);
        if (responseJson.containsKey('feedback') &&
            responseJson['feedback'] is String) {
          return responseJson['feedback'] as String;
        } else {
          throw Exception('پاسخ نامعتبر از سرور دریافت شد.');
        }
      } else if (response.statusCode == 400) {
        throw Exception('متن ارسالی نامعتبر است. لطفاً متن خود را بررسی کنید.');
      } else if (response.statusCode == 429) {
        throw Exception(
            'تعداد درخواست‌های شما بیش از حد مجاز است. لطفاً کمی صبر کنید.');
      } else if (response.statusCode >= 500) {
        throw Exception('خطای داخلی سرور. لطفاً بعداً تلاش کنید.');
      } else {
        throw Exception(
            'خطا در دریافت بازخورد هوش مصنوعی: ${response.statusCode}');
      }
    } on SocketException {
      throw Exception(
          'خطا در اتصال به اینترنت. لطفاً اتصال خود را بررسی کنید.');
    } on FormatException {
      throw Exception('خطا در تجزیه پاسخ دریافتی از سرور.');
    } on Exception catch (e) {
      // Re-throw custom exceptions
      if (e.toString().contains('درخواست منقضی شد') ||
          e.toString().contains('متن ارسالی نامعتبر است') ||
          e.toString().contains('تعداد درخواست‌های شما بیش از حد مجاز است') ||
          e.toString().contains('خطای داخلی سرور') ||
          e.toString().contains('خطا در اتصال به اینترنت') ||
          e.toString().contains('خطا در تجزیه پاسخ دریافتی') ||
          e.toString().contains('پاسخ نامعتبر از سرور') ||
          e.toString().contains('خطا در دریافت بازخورد هوش مصنوعی')) {
        rethrow;
      }
      throw Exception('خطای غیرمنتظره: ${e.toString()}');
    } catch (e) {
      throw Exception('خطای غیرمنتظره در دریافت بازخورد هوش مصنوعی.');
    }
  }

  Future<List<Question>> fetchQuiz(int lessonId) async {
    try {
      final uri = Uri.parse('$_baseUrl/lessons/$lessonId/quiz');

      final response = await http
          .get(
        uri,
        headers: _headers,
      )
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('درخواست منقضی شد. لطفاً دوباره تلاش کنید.');
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> quizJson = json.decode(response.body);

        if (quizJson.isEmpty) {
          return []; // Return empty list for lessons without quiz
        }

        return quizJson
            .map((questionJson) =>
                Question.fromJson(questionJson as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 404) {
        throw Exception('آزمون این درس یافت نشد.');
      } else if (response.statusCode == 500) {
        throw Exception('خطای سرور در بارگذاری آزمون. لطفاً بعداً تلاش کنید.');
      } else {
        throw Exception('خطا در دریافت آزمون: ${response.statusCode}');
      }
    } on SocketException {
      throw Exception(
          'خطا در اتصال به اینترنت. لطفاً اتصال خود را بررسی کنید.');
    } on FormatException {
      throw Exception('خطا در تجزیه داده‌های آزمون دریافتی از سرور.');
    } on Exception catch (e) {
      // Re-throw custom exceptions
      if (e.toString().contains('درخواست منقضی شد') ||
          e.toString().contains('آزمون این درس یافت نشد') ||
          e.toString().contains('خطای سرور در بارگذاری آزمون') ||
          e.toString().contains('خطا در اتصال به اینترنت') ||
          e.toString().contains('خطا در تجزیه داده‌های آزمون') ||
          e.toString().contains('خطا در دریافت آزمون')) {
        rethrow;
      }
      throw Exception('خطای غیرمنتظره: ${e.toString()}');
    } catch (e) {
      throw Exception('خطای غیرمنتظره در دریافت آزمون.');
    }
  }
}
