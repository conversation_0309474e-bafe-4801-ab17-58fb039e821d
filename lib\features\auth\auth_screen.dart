import 'package:flutter/material.dart';

/// Authentication screen for user login/registration via mobile OTP
/// Handles two stages: mobile number entry and OTP verification
class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  // Controllers for managing text input
  late TextEditingController _mobileController;
  late TextEditingController _otpController;

  // State management variables
  bool _isOtpSent =
      false; // Toggle between mobile input and OTP verification stages
  bool _isLoading = false; // Show loading indicators during API calls

  @override
  void initState() {
    super.initState();
    // Initialize text controllers
    _mobileController = TextEditingController();
    _otpController = TextEditingController();
  }

  @override
  void dispose() {
    // Clean up controllers to prevent memory leaks
    _mobileController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  /// Sends OTP to the entered mobile number
  /// Currently uses placeholder logic - will be replaced with actual API call
  Future<void> _sendOtp() async {
    // Validate mobile number input
    if (_mobileController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لطفاً شماره موبایل خود را وارد کنید'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Set loading state
      setState(() {
        _isLoading = true;
      });

      // Simulate API call delay - replace with actual ApiService.sendOtp() call
      await Future.delayed(const Duration(seconds: 2));

      // Update state to show OTP verification stage
      setState(() {
        _isOtpSent = true;
        _isLoading = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('کد تایید به شماره ${_mobileController.text} ارسال شد'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Handle errors
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در ارسال کد: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Verifies the entered OTP code
  /// Currently uses placeholder logic - will be replaced with actual API call
  Future<void> _verifyOtp() async {
    // Validate OTP input
    if (_otpController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لطفاً کد تایید را وارد کنید'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Set loading state
      setState(() {
        _isLoading = true;
      });

      // Simulate API call delay - replace with actual ApiService.verifyOtp() call
      await Future.delayed(const Duration(seconds: 2));

      // Debug print - will be replaced with actual navigation logic
      print('OTP verified successfully for ${_mobileController.text}');

      // Reset loading state
      setState(() {
        _isLoading = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ورود موفقیت‌آمیز بود'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // TODO: Navigate to main app screen after successful verification
    } catch (e) {
      // Handle errors
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در تایید کد: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Builds the mobile number input stage UI
  Widget _buildMobileInputStage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Mobile number input field
        TextField(
          controller: _mobileController,
          keyboardType: TextInputType.phone,
          textAlign: TextAlign.center,
          textDirection: TextDirection.ltr, // LTR for phone numbers
          decoration: const InputDecoration(
            labelText: 'شماره موبایل',
            border: OutlineInputBorder(),
            prefixText: '+98 ',
          ),
        ),
        const SizedBox(height: 20),
        // Send OTP button or loading indicator
        _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ElevatedButton(
                onPressed: _sendOtp,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('ارسال کد'),
              ),
      ],
    );
  }

  /// Builds the OTP verification stage UI
  Widget _buildOtpVerificationStage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Information text showing where OTP was sent
        Text(
          'کد تایید به شماره ${_mobileController.text} ارسال شد',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
        // OTP input field
        TextField(
          controller: _otpController,
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          maxLength: 6,
          decoration: const InputDecoration(
            labelText: 'کد تایید',
            border: OutlineInputBorder(),
            counterText: '', // Hide character counter
          ),
        ),
        const SizedBox(height: 20),
        // Verify OTP button or loading indicator
        _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ElevatedButton(
                onPressed: _verifyOtp,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('تایید کد'),
              ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // App bar with Persian title
      appBar: AppBar(
        title: const Text('ورود یا ثبت‌نام'),
        centerTitle: true,
      ),
      // Main body with padding
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isOtpSent
            ? _buildOtpVerificationStage() // Show OTP verification stage
            : _buildMobileInputStage(), // Show mobile number input stage
      ),
    );
  }
}
