import 'package:flutter/material.dart';

class AppTheme {
  // Professional color palette
  static const Color kPrimaryColor = Color(0xFF00BFA5); // Vibrant Teal
  static const Color kBackgroundColorStart = Color(0xFF1A237E); // Dark Indigo
  static const Color kBackgroundColorEnd = Color(0xFF121212); // Very Dark Grey
  static const Color kCardColor = Color(0xFF2C2C3E); // Dark Slate Blue
  static const Color kTextColor =
      Color(0xFFE0E0E0); // Off-white for better contrast
  static const Color kLockedColor = Color(0xFFFFA000); // Amber for locked items
  static const Color kSuccessColor = Color(0xFF4CAF50); // Green for free items
  static ThemeData darkTheme() {
    return ThemeData(
      brightness: Brightness.dark,
      scaffoldBackgroundColor: Colors.transparent,
      primarySwatch: Colors.teal,
      primaryColor: kPrimaryColor,
      cardColor: kCardColor,
      fontFamily: 'Samim',
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
      ),
      iconTheme: const IconThemeData(
        color: kTextColor,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: Colors.white,
          fontFamily: 'Samim',
          fontSize: 32,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          color: Colors.white,
          fontFamily: 'Samim',
          fontSize: 28,
          fontWeight: FontWeight.bold,
        ),
        displaySmall: TextStyle(
          color: Colors.white,
          fontFamily: 'Samim',
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
        headlineLarge: TextStyle(
          color: Colors.white,
          fontFamily: 'Samim',
          fontSize: 22,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: Colors.white,
          fontFamily: 'Samim',
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        headlineSmall: TextStyle(
          color: Colors.white,
          fontFamily: 'Samim',
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        titleLarge: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        titleMedium: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        titleSmall: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        bodyLarge: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 16,
        ),
        bodyMedium: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 14,
        ),
        bodySmall: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 12,
        ),
        labelLarge: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        labelMedium: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        labelSmall: TextStyle(
          color: kTextColor,
          fontFamily: 'Samim',
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

class AppGradients {
  static const LinearGradient mainBackground = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppTheme.kBackgroundColorStart,
      AppTheme.kBackgroundColorEnd,
    ],
  );

  static LinearGradient appBarBackground = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppTheme.kPrimaryColor,
      Colors.teal[700]!,
    ],
  );
}
