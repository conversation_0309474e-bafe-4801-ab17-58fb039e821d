import 'package:flutter/foundation.dart';
import '../../../core/services/api_service.dart';

class AiExerciseProvider extends ChangeNotifier {
  final ApiService _apiService;

  AiExerciseProvider(this._apiService);

  // Private state variables
  bool _isLoading = false;
  String? _feedback;
  String? _error;

  // Public getters
  bool get isLoading => _isLoading;
  String? get feedback => _feedback;
  String? get error => _error;

  /// Fetch AI feedback for the provided user text
  Future<void> fetchAiFeedback({required String userText}) async {
    // Validate input
    final trimmedText = userText.trim();
    if (trimmedText.isEmpty) {
      _setError('لطفاً متنی برای دریافت بازخورد وارد کنید.');
      return;
    }

    _setLoading(true);
    _clearFeedback();
    _clearError();

    try {
      final receivedFeedback = await _apiService.getAiFeedback(userText: trimmedText);
      _setFeedback(receivedFeedback);
      _clearError();
    } catch (e) {
      _setError(_formatErrorMessage(e.toString()));
      _clearFeedback();
    } finally {
      _setLoading(false);
    }
  }

  /// Clear all state variables to initial values
  void clear() {
    _isLoading = false;
    _feedback = null;
    _error = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setFeedback(String feedback) {
    _feedback = feedback;
    notifyListeners();
  }

  void _clearFeedback() {
    _feedback = null;
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Format error messages to be user-friendly
  String _formatErrorMessage(String errorMessage) {
    // Remove 'Exception: ' prefix if present
    String cleanMessage = errorMessage.replaceFirst('Exception: ', '');
    
    // Return the clean Persian error message
    return cleanMessage;
  }
}
