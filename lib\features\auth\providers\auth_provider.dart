import 'package:flutter/material.dart';
import '../../../core/services/api_service.dart';

/// Provider for managing authentication state and API calls
/// Handles OTP sending and verification with loading states
class AuthProvider extends ChangeNotifier {
  final ApiService _apiService;

  // Private state variables
  bool _isLoading = false;

  // Constructor - requires ApiService instance
  AuthProvider(this._apiService);

  // Public getters
  bool get isLoading => _isLoading;

  /// Sends OTP to the specified mobile number
  /// Returns true if successful, false otherwise
  /// Manages loading state automatically
  Future<bool> sendOtp(String mobile) async {
    try {
      // Set loading state
      _isLoading = true;
      notifyListeners();

      // Call API service
      final result = await _apiService.sendOtp(mobile);
      return result;
    } finally {
      // Always reset loading state
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Verifies the OTP for the specified mobile number
  /// Returns true if verification successful, false otherwise
  /// Manages loading state automatically
  Future<bool> verifyOtp(String mobile, String otp) async {
    try {
      // Set loading state
      _isLoading = true;
      notifyListeners();

      // Call API service
      final result = await _apiService.verifyOtp(mobile, otp);
      return result;
    } finally {
      // Always reset loading state
      _isLoading = false;
      notifyListeners();
    }
  }
}
