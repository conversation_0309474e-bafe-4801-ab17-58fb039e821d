import 'package:flutter/foundation.dart';
import '../../../core/services/api_service.dart';
import '../models/quiz_models.dart';

class QuizProvider extends ChangeNotifier {
  final ApiService _apiService;

  QuizProvider(this._apiService);

  // Private state variables
  List<Question> _questions = [];
  bool _isLoading = false;
  String? _error;
  int _currentQuestionIndex = 0;
  final Map<int, int> _selectedAnswers = {}; // questionId -> selectedOptionId
  bool _quizCompleted = false;
  int _score = 0;

  // Public getters
  List<Question> get questions => _questions;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get currentQuestionIndex => _currentQuestionIndex;
  Map<int, int> get selectedAnswers => _selectedAnswers;
  bool get quizCompleted => _quizCompleted;
  int get score => _score;

  // Computed getters
  Question? get currentQuestion {
    if (_questions.isEmpty || _currentQuestionIndex >= _questions.length) {
      return null;
    }
    return _questions[_currentQuestionIndex];
  }

  bool get hasQuestions => _questions.isNotEmpty;
  bool get hasError => _error != null;

  bool get canProceed {
    final current = currentQuestion;
    if (current == null) return false;
    return _selectedAnswers.containsKey(current.id);
  }

  int get totalQuestions => _questions.length;

  double get progressPercentage {
    if (_questions.isEmpty) return 0.0;
    return (_currentQuestionIndex + 1) / _questions.length;
  }

  bool get isFirstQuestion => _currentQuestionIndex == 0;
  bool get isLastQuestion => _currentQuestionIndex == _questions.length - 1;

  /// Load quiz data from API
  Future<void> loadQuiz(int lessonId) async {
    _setLoading(true);
    _clearError();

    try {
      final fetchedQuestions = await _apiService.fetchQuiz(lessonId);
      _questions = fetchedQuestions;
      _resetQuizState();
      _clearError();
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _questions = [];
    } finally {
      _setLoading(false);
    }
  }

  /// Select an answer for the current question
  void selectAnswer(int questionId, int optionId) {
    _selectedAnswers[questionId] = optionId;
    notifyListeners();
  }

  /// Move to next question
  void nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  /// Move to previous question
  void previousQuestion() {
    if (_currentQuestionIndex > 0) {
      _currentQuestionIndex--;
      notifyListeners();
    }
  }

  /// Submit quiz and calculate score
  Future<void> submitQuiz() async {
    _calculateScore();
    _quizCompleted = true;
    notifyListeners();
  }

  /// Reset quiz to initial state for retaking
  void resetQuiz() {
    _currentQuestionIndex = 0;
    _selectedAnswers.clear();
    _quizCompleted = false;
    _score = 0;
    _clearError();
    notifyListeners();
  }

  /// Check if a specific answer is selected
  bool isAnswerSelected(int questionId, int optionId) {
    return _selectedAnswers[questionId] == optionId;
  }

  /// Get selected option for a question
  int? getSelectedOption(int questionId) {
    return _selectedAnswers[questionId];
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  void _resetQuizState() {
    _currentQuestionIndex = 0;
    _selectedAnswers.clear();
    _quizCompleted = false;
    _score = 0;
  }

  void _calculateScore() {
    _score = 0;
    for (final question in _questions) {
      final selectedOptionId = _selectedAnswers[question.id];
      if (selectedOptionId != null) {
        final selectedOption = question.options.firstWhere(
          (option) => option.id == selectedOptionId,
          orElse: () =>
              QuestionOption(id: -1, optionText: '', isCorrect: false),
        );
        if (selectedOption.isCorrect) {
          _score++;
        }
      }
    }
  }

  @override
  void dispose() {
    // Clean up any resources if needed
    super.dispose();
  }
}
