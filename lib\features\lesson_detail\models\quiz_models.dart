class QuestionOption {
  final int id;
  final String optionText;
  final bool isCorrect;

  QuestionOption({
    required this.id,
    required this.optionText,
    required this.isCorrect,
  });

  factory QuestionOption.fromJson(Map<String, dynamic> json) {
    return QuestionOption(
      id: json['id'] as int? ?? 0,
      optionText: json['option_text'] as String? ?? '',
      isCorrect: json['is_correct'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'option_text': optionText,
      'is_correct': isCorrect,
    };
  }

  @override
  String toString() {
    return 'QuestionOption{id: $id, optionText: $optionText, isCorrect: $isCorrect}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuestionOption &&
        other.id == id &&
        other.optionText == optionText &&
        other.isCorrect == isCorrect;
  }

  @override
  int get hashCode {
    return id.hashCode ^ optionText.hashCode ^ isCorrect.hashCode;
  }
}

class Question {
  final int id;
  final String questionText;
  final List<QuestionOption> options;

  Question({
    required this.id,
    required this.questionText,
    required this.options,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    final optionsJson = json['options'] as List<dynamic>? ?? [];
    final options = optionsJson
        .map((optionJson) => QuestionOption.fromJson(optionJson as Map<String, dynamic>))
        .toList();

    return Question(
      id: json['id'] as int? ?? 0,
      questionText: json['question_text'] as String? ?? '',
      options: options,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question_text': questionText,
      'options': options.map((option) => option.toJson()).toList(),
    };
  }

  QuestionOption? get correctOption {
    try {
      return options.firstWhere((option) => option.isCorrect);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'Question{id: $id, questionText: $questionText, options: ${options.length}}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Question &&
        other.id == id &&
        other.questionText == questionText &&
        other.options.length == options.length;
  }

  @override
  int get hashCode {
    return id.hashCode ^ questionText.hashCode ^ options.length.hashCode;
  }
}
